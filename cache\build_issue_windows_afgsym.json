{"data": {"platform": "windows", "errorMessage": "link error", "errorType": "link", "solutions": [{"title": "GitHub Issue解决方案: Calling Label::setString after Label::getLetter causes layout issues", "description": "- axmol version:\n- devices test on:\n- developing environments\n   - NDK version: r23c\n   - Xcode version: 14.2+\n   - Visual Studio: \n      - VS version: 2022 (17.9+)\n      - MSVC version: 19.39+\n      ", "steps": ["查看完整的Issue讨论", "按照Issue中提到的解决步骤操作", "如果问题仍然存在，可以在Issue中留言求助"], "codeChanges": [], "configChanges": [], "priority": "medium", "verified": false}, {"title": "GitHub Issue解决方案: Odd behavior with label overflow setting: Label::Overflow::SHRINK", "description": "- axmol version: 2.5\n- devices test on:\n- developing environments\n   - NDK version: r23c\n   - Xcode version: 14.2+\n   - Visual Studio: \n      - VS version: 2022 (17.9+)\n      - MSVC version: 19.39+\n  ", "steps": ["查看完整的Issue讨论", "按照Issue中提到的解决步骤操作", "如果问题仍然存在，可以在Issue中留言求助"], "codeChanges": [], "configChanges": [], "priority": "medium", "verified": false}, {"title": "GitHub Issue解决方案: Unresolved external symbol while using prebuilts workflow (linking opus lib error)", "description": "- axmol version - 2.6.0 (6ee3fd18b702521b29cacd4bfd21d342aaf153d5)\n- devices test on - Windows\n- developing environments\n  - MSVC version - 19.43.34808\n  - cmake version - 3.31.3\n  - IDE - no (build v", "steps": ["查看完整的Issue讨论", "按照Issue中提到的解决步骤操作", "如果问题仍然存在，可以在Issue中留言求助"], "codeChanges": [], "configChanges": [], "priority": "medium", "verified": false}, {"title": "GitHub Issue解决方案: [LUA] Unresolved external symbol while using prebuilts workflow (lua project link error)", "description": "- axmol version - 2.6.0 (6ee3fd18b702521b29cacd4bfd21d342aaf153d5)\n- devices test on - Windows\n- developing environments\n  - MSVC version - 19.43.34808\n  - cmake version - 3.31.3\n  - IDE - no (build v", "steps": ["查看完整的Issue讨论", "按照Issue中提到的解决步骤操作", "如果问题仍然存在，可以在Issue中留言求助"], "codeChanges": [], "configChanges": [], "priority": "medium", "verified": false}], "relatedIssues": ["https://github.com/axmolengine/axmol/issues?q=is%3Aissue+windows+build", "https://github.com/axmolengine/axmol/wiki/Build-windows"]}, "timestamp": 1750517189692, "ttl": 3600000, "key": "build_issue_windows_afgsym"}